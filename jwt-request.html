<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JWT请求页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        input[type="text"]:focus {
            border-color: #007bff;
            outline: none;
        }
        
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin-top: 10px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>JWT请求页面</h1>
        
        <div class="form-group">
            <label for="employeeid">员工ID:</label>
            <input type="text" id="employeeid" placeholder="请输入员工ID" />
        </div>
        
        <button onclick="sendRequest()">发送请求</button>
        
        <div class="loading" id="loading">
            正在发送请求...
        </div>
        
        <div class="result" id="result"></div>
    </div>

    <script>
        async function sendRequest() {
            const employeeidInput = document.getElementById('employeeid');
            const button = document.querySelector('button');
            const loading = document.getElementById('loading');
            const result = document.getElementById('result');
            
            // 获取输入值
            const employeeid = employeeidInput.value.trim();
            
            // 验证输入
            if (!employeeid) {
                showResult('请输入员工ID', 'error');
                return;
            }
            
            // 显示加载状态
            button.disabled = true;
            loading.style.display = 'block';
            result.style.display = 'none';
            
            try {
                // 将employeeid转换为base64
                const base64EmployeeId = btoa(employeeid);
                
                // 发送POST请求
                const response = await fetch('http://139.196.240.30:3000/jwt/new', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        employeeid: base64EmployeeId
                    })
                });
                
                // 处理响应
                if (response.ok) {
                    const responseData = await response.text();
                    showResult(`请求成功！响应: ${responseData}`, 'success');
                } else {
                    showResult(`请求失败！状态码: ${response.status}`, 'error');
                }
                
            } catch (error) {
                showResult(`请求出错: ${error.message}`, 'error');
            } finally {
                // 恢复按钮状态
                button.disabled = false;
                loading.style.display = 'none';
            }
        }
        
        function showResult(message, type) {
            const result = document.getElementById('result');
            result.textContent = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }
        
        // 允许按Enter键提交
        document.getElementById('employeeid').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendRequest();
            }
        });
    </script>
</body>
</html>
